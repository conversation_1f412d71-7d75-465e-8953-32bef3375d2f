package responses

import (
	"digital-transformation-api/libs/apps"
)

// ServiceResponse represents a response from service layer that includes version
type ServiceResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
	Version string      `json:"version"`
}

// NewServiceResponse creates a new service response with the current API version
func NewServiceResponse(success bool, data interface{}) *ServiceResponse {
	return &ServiceResponse{
		Success: success,
		Data:    data,
		Version: apps.ApiVersion,
	}
}

// NewServiceSuccessResponse creates a new successful service response with the current API version
func NewServiceSuccessResponse(data interface{}) *ServiceResponse {
	return &ServiceResponse{
		Success: true,
		Data:    data,
		Version: apps.ApiVersion,
	}
}

// NewServiceSuccessResponseWithMessage creates a new successful service response with a message and the current API version
func NewServiceSuccessResponseWithMessage(data interface{}, message string) *ServiceResponse {
	return &ServiceResponse{
		Success: true,
		Data:    data,
		Message: message,
		Version: apps.ApiVersion,
	}
}

// WithVersion adds the current API version to any response structure
// This is useful for existing response structures that need to include version
func WithVersion() string {
	return apps.ApiVersion
}

// WrapWithVersion wraps any data with a standard response structure including version
func WrapWithVersion(success bool, data interface{}, message string) map[string]interface{} {
	response := map[string]interface{}{
		"success": success,
		"version": apps.ApiVersion,
	}
	
	if data != nil {
		response["data"] = data
	}
	
	if message != "" {
		response["message"] = message
	}
	
	return response
}

// CreateVersionedResponse creates a versioned response for any data structure
// This is useful when you need to maintain existing response structures but add version
func CreateVersionedResponse(data interface{}) map[string]interface{} {
	return map[string]interface{}{
		"success": true,
		"data":    data,
		"version": apps.ApiVersion,
	}
}
