package responses

import (
	"digital-transformation-api/libs/apps"
	"net/http"

	"github.com/gin-gonic/gin"
)

// BaseResponse represents the standard API response structure
type BaseResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
	Version string      `json:"version"`
}

// ErrorResponse represents the standard API error response structure
type ErrorResponse struct {
	Success     bool   `json:"success"`
	Code        string `json:"code,omitempty"`
	Message     string `json:"message,omitempty"`
	Description string `json:"description,omitempty"`
	Version     string `json:"version"`
}

// NewSuccessResponse creates a new success response with the current API version
func NewSuccessResponse(data interface{}) *BaseResponse {
	return &BaseResponse{
		Success: true,
		Data:    data,
		Version: apps.ApiVersion,
	}
}

// NewSuccessResponseWithMessage creates a new success response with a message and the current API version
func NewSuccessResponseWithMessage(data interface{}, message string) *BaseResponse {
	return &BaseResponse{
		Success: true,
		Data:    data,
		Message: message,
		Version: apps.ApiVersion,
	}
}

// NewErrorResponse creates a new error response with the current API version
func NewErrorResponse(code, message, description string) *ErrorResponse {
	return &ErrorResponse{
		Success:     false,
		Code:        code,
		Message:     message,
		Description: description,
		Version:     apps.ApiVersion,
	}
}

// SendSuccess sends a successful JSON response with the current API version
func SendSuccess(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusOK, NewSuccessResponse(data))
}

// SendSuccessWithMessage sends a successful JSON response with a message and the current API version
func SendSuccessWithMessage(ctx *gin.Context, data interface{}, message string) {
	ctx.JSON(http.StatusOK, NewSuccessResponseWithMessage(data, message))
}

// SendError sends an error JSON response with the current API version
func SendError(ctx *gin.Context, statusCode int, code, message, description string) {
	ctx.JSON(statusCode, NewErrorResponse(code, message, description))
}

// SendCreated sends a 201 Created response with the current API version
func SendCreated(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusCreated, NewSuccessResponse(data))
}

// SendAccepted sends a 202 Accepted response with the current API version
func SendAccepted(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusAccepted, NewSuccessResponse(data))
}

// SendNoContent sends a 204 No Content response with the current API version
func SendNoContent(ctx *gin.Context) {
	ctx.JSON(http.StatusNoContent, NewSuccessResponse(nil))
}
