package calldb

import (
	"digital-transformation-api/internal/common/domain"
	"digital-transformation-api/libs/apps"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type Port interface {
	GetStaffInfo(request *GetStaffInfoRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetStaffInfoResponse, errs.Error)
	RegisterStaff(request *RegisterStaffRequest, rctx *contexts.RouteContext, l logger.Logger) (*RegisterStaffResponse, errs.Error)
}

// GetStaffInfo request and response types
type GetStaffInfoRequest struct {
	LineToken string `validate:"required"`
}

type GetStaffInfoResponse struct {
	Success bool         `json:"success"`
	Data    domain.Staff `json:"data"`
	Version string       `json:"version"`
}

// NewGetStaffInfoResponse creates a new GetStaffInfoResponse with the current API version
func NewGetStaffInfoResponse(success bool, data domain.Staff) *GetStaffInfoResponse {
	return &GetStaffInfoResponse{
		Success: success,
		Data:    data,
		Version: apps.ApiVersion,
	}
}

// RegisterStaff request and response types
type RegisterStaffRequest struct {
	EmployeeID string `validate:"required"`
	Phone      string `validate:"required"`
	LineToken  string `validate:"required"`
}

type RegisterStaffResponse struct {
	Success bool         `json:"success"`
	Data    domain.Staff `json:"data"`
	Version string       `json:"version"`
}

// NewRegisterStaffResponse creates a new RegisterStaffResponse with the current API version
func NewRegisterStaffResponse(success bool, data domain.Staff) *RegisterStaffResponse {
	return &RegisterStaffResponse{
		Success: success,
		Data:    data,
		Version: apps.ApiVersion,
	}
}
