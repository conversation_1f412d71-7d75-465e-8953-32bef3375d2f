package staff

import (
	"digital-transformation-api/infrastructure"
	"digital-transformation-api/internal/common/domain"
	calldb "digital-transformation-api/internal/line/port/call-db"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type service struct {
	callDb calldb.Port
}

func New(callDb calldb.Port) Service {
	return &service{
		callDb: callDb,
	}
}

func (s *service) Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error) {
	if rctx == nil || rctx.Header.LineToken == "" {
		return nil, errs.NewBadRequestError()
	}

	dbResp, err := s.callDb.GetStaffInfo(&calldb.GetStaffInfoRequest{LineToken: rctx.Header.LineToken}, rctx, l)
	if err != nil {
		return nil, err
	}
	if dbResp == nil {
		return NewResponse(true, domain.Staff{}), nil
	}

	return NewResponse(dbResp.Success, dbResp.Data), nil
}

func (s *service) Register(request *RegisterRequest, rctx *contexts.RouteContext, l logger.Logger) (*RegisterResponse, errs.Error) {
	if rctx == nil || rctx.Header.LineToken == "" {
		return nil, errs.NewBadRequestError()
	}

	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	dbResp, err := s.callDb.RegisterStaff(&calldb.RegisterStaffRequest{
		EmployeeID: request.EmployeeID,
		Phone:      request.Phone,
		LineToken:  rctx.Header.LineToken,
	}, rctx, l)
	if err != nil {
		return nil, err
	}

	return NewRegisterResponse(dbResp.Success, dbResp.Data), nil
}
