package staff

import (
	"digital-transformation-api/internal/common/domain"
	"digital-transformation-api/libs/apps"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type Service interface {
	Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error)
	Register(request *RegisterRequest, rctx *contexts.RouteContext, l logger.Logger) (*RegisterResponse, errs.Error)
}

type Request struct{}

type Response struct {
	Success bool         `json:"success"`
	Data    domain.Staff `json:"data"`
	Version string       `json:"version"`
}

// NewResponse creates a new Response with the current API version
func NewResponse(success bool, data domain.Staff) *Response {
	return &Response{
		Success: success,
		Data:    data,
		Version: apps.ApiVersion,
	}
}

type RegisterRequest struct {
	EmployeeID string `json:"employeeId" validate:"required"`
	Phone      string `json:"phone" validate:"required"`
}

type RegisterResponse struct {
	Success bool         `json:"success"`
	Data    domain.Staff `json:"data"`
	Version string       `json:"version"`
}

// NewRegisterResponse creates a new RegisterResponse with the current API version
func NewRegisterResponse(success bool, data domain.Staff) *RegisterResponse {
	return &RegisterResponse{
		Success: success,
		Data:    data,
		Version: apps.ApiVersion,
	}
}
